00:10:26.276 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:10:26.289 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:10:40.844 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 907852 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:10:40.850 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:10:42.360 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:10:42.361 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:10:42.361 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:10:42.498 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:10:43.339 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:10:43.699 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:10:44.287 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:10:44.288 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:10:45.007 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:10:45.052 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:10:45.053 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:10:45.195 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:10:45.201 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:10:45.203 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:10:45.204 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:10:45.204 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:10:45.205 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:10:45.205 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:10:45.205 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:10:45.206 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:10:45.209 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:10:45.269 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:10:45.286 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 5.085 seconds (JVM running for 5.698)
00:10:51.159 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:14:00.580 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:14:00.584 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:19:29.039 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 919555 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:19:29.041 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:19:29.805 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:19:29.806 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:19:29.806 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:19:29.859 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:19:30.387 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:19:30.652 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:19:31.501 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:19:31.502 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:19:31.745 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:19:31.768 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:19:31.769 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:19:31.833 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:19:31.836 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:19:31.839 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:19:31.839 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:19:31.839 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:19:31.839 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:19:31.840 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:19:31.840 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:19:31.840 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:19:31.842 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:19:31.880 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:19:31.893 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.69 seconds (JVM running for 3.076)
00:19:34.881 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:20:10.510 [http-nio-9550-exec-3] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,41] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
00:20:10.511 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,34] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
00:20:10.511 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,53] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
00:20:10.514 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,149] - 查询检验记录数据，车辆ID: 0822
00:20:10.514 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,200] - 查询到检验记录数据 4 条
00:20:10.519 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1572] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
00:20:10.696 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,770] - 已设置文档为横向纸张
00:20:10.696 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1616] - 创建新JSON格式表格，总行数: 11, 总列数: 8
00:20:10.747 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1687] - 设置表格总宽度: 880px (13200twips)
00:20:10.803 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
00:20:10.808 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
00:20:10.809 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
00:20:10.811 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
00:20:10.812 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 151px (2265twips)
00:20:10.814 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:10.815 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:10.816 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:10.817 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:10.818 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1793] - 应用JSON格式表头合并单元格，数量: 7
00:20:10.820 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
00:20:10.823 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
00:20:10.824 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
00:20:10.825 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
00:20:10.825 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列3-4, 跨度2列
00:20:10.827 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列3-4
00:20:10.828 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
00:20:10.828 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
00:20:10.829 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
00:20:10.899 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1593] - 新JSON格式Word文档导出完成，文件大小: 3184 bytes
00:20:10.906 [http-nio-9550-exec-3] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,69] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC1%E9%A1%B5_20250825_002010.docx, 大小: 3184 bytes
00:20:52.705 [http-nio-9550-exec-9] INFO  c.l.w.c.CheckRecordExportController - [exportAllPages,91] - 接收到导出全部页面请求，车辆ID: 0822
00:20:52.706 [http-nio-9550-exec-9] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,34] - 开始导出检验记录，车辆ID: 0822, 导出类型: all_pages
00:20:52.706 [http-nio-9550-exec-9] INFO  c.l.w.s.CheckRecordExportService - [exportAllPages,82] - 导出全部页面，车辆ID: 0822
00:20:52.711 [http-nio-9550-exec-9] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,149] - 查询检验记录数据，车辆ID: 0822
00:20:52.711 [http-nio-9550-exec-9] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,200] - 查询到检验记录数据 4 条
00:20:52.712 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1572] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
00:20:52.714 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,770] - 已设置文档为横向纸张
00:20:52.715 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1616] - 创建新JSON格式表格，总行数: 16, 总列数: 8
00:20:52.718 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1687] - 设置表格总宽度: 880px (13200twips)
00:20:52.724 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
00:20:52.726 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
00:20:52.727 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
00:20:52.728 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
00:20:52.729 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 151px (2265twips)
00:20:52.730 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:52.731 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:52.732 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:52.733 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:52.734 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 120px (1800twips)
00:20:52.735 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:52.736 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:52.736 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:52.737 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:52.738 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1793] - 应用JSON格式表头合并单元格，数量: 7
00:20:52.738 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
00:20:52.739 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
00:20:52.740 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
00:20:52.740 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
00:20:52.740 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列3-4, 跨度2列
00:20:52.740 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列3-4
00:20:52.740 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
00:20:52.741 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
00:20:52.741 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
00:20:52.751 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1593] - 新JSON格式Word文档导出完成，文件大小: 3299 bytes
00:20:52.756 [http-nio-9550-exec-9] INFO  c.l.w.c.CheckRecordExportController - [exportAllPages,114] - 检验记录全部页面导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E5%85%A8%E9%83%A8%E9%A1%B5%E9%9D%A2_20250825_002052.docx, 大小: 3299 bytes
00:21:28.454 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:21:28.456 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
08:51:00.465 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 26535 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
08:51:00.468 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
08:51:01.390 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
08:51:01.390 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
08:51:01.391 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
08:51:01.428 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
08:51:01.997 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
08:51:02.262 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
08:51:02.570 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
08:51:02.571 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
08:51:02.832 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
08:51:02.858 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
08:51:02.858 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
08:51:02.922 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
08:51:02.927 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
08:51:02.929 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
08:51:02.929 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
08:51:02.930 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
08:51:02.930 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
08:51:02.930 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
08:51:02.930 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
08:51:02.931 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
08:51:02.933 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
08:51:02.966 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
08:51:02.980 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.916 seconds (JVM running for 3.445)
09:00:52.288 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:17:23.560 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
09:17:23.562 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
09:28:48.896 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 73640 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
09:28:48.899 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
09:28:49.747 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
09:28:49.747 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:28:49.747 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
09:28:49.782 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:28:50.271 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
09:28:50.530 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
09:28:50.945 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
09:28:50.946 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
09:28:51.235 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
09:28:51.256 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
09:28:51.257 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
09:28:51.320 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
09:28:51.324 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
09:28:51.326 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
09:28:51.327 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
09:28:51.327 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
09:28:51.327 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
09:28:51.327 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
09:28:51.327 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
09:28:51.328 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
09:28:51.329 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
09:28:51.364 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
09:28:51.378 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.884 seconds (JVM running for 3.264)
09:29:21.169 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
